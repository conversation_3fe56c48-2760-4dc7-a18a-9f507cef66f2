// it's the shader that will update the particles pixels by gpgpu technique
// it's here we apply to handle the flow field

// uniform sampler2D uParticles  // no need declare, it's already injected in the GPUComputationRenderer

void main()
{

    vec2 uv = gl_FragCoord.xy / resolution.xy; // need classic UV coordinates
    vec4 particle = texture(uParticles, uv); // coordinate
    particle.y += 0.01;
    gl_FragColor = particle;

    // gl_FragColor = vec4(uv, 1.0, 1.0);

}

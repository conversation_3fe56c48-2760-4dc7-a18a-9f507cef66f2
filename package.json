{"name": "my-threejs-journey", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-syntax-highlighter": "^15.5.13", "@vercel/analytics": "^1.5.0", "cannon-es": "^0.20.0", "framer-motion": "^12.7.3", "gsap": "^3.12.7", "lil-gui": "^0.20.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3", "react-router-dom": "^7.5.3", "react-syntax-highlighter": "^15.6.1", "three": "^0.176.0"}, "devDependencies": {"@eslint/js": "^9.24.0", "@tailwindcss/vite": "^4.1.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/three": "^0.176.0", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-glsl": "^1.4.0", "vite-plugin-pages": "^0.33.0"}}
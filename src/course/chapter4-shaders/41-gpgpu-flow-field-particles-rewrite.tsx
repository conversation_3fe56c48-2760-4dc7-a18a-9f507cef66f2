import { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
import {
  GPUComputationRenderer,
  Variable,
} from 'three/addons/misc/GPUComputationRenderer.js';
import GUI from 'lil-gui';
// 路径按你项目实际调整
import particlesVertexShader from './shaders/41/particles/vertex.glsl';
import particlesFragmentShader from './shaders/41/particles/fragment.glsl';
import gpgpuParticlesShader from './shaders/41/gpgpu/particles.glsl';

const MODEL_PATH = '/models/suzanne.glb'; // 你可以换成自己的模型

type ParticlesType = {
  geometry?: THREE.BufferGeometry;
  material?: THREE.ShaderMaterial;
  points?: THREE.Points;
};

type GPGPUType = {
  size?: number;
  computation?: GPUComputationRenderer;
  particlesVariable?: Variable;
  debug?: THREE.Mesh;
};

const GPGPUFlowFieldParticles: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Debug
    const gui = new GUI({ width: 340 });
    const debugObject: Record<string, unknown> = {};

    // Scene
    const scene = new THREE.Scene();

    // Loaders
    const dracoLoader = new DRACOLoader();
    dracoLoader.setDecoderPath('/draco/');
    const gltfLoader = new GLTFLoader();
    gltfLoader.setDRACOLoader(dracoLoader);

    // Sizes
    const sizes = {
      width: window.innerWidth,
      height: window.innerHeight,
      pixelRatio: Math.min(window.devicePixelRatio, 2),
    };

    // Camera
    const camera = new THREE.PerspectiveCamera(
      35,
      sizes.width / sizes.height,
      0.1,
      100
    );
    camera.position.set(4.5, 4, 11);
    scene.add(camera);

    // Renderer
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      antialias: true,
    });
    debugObject.clearColor = '#29191f';
    renderer.setClearColor(debugObject.clearColor as string);
    renderer.setSize(sizes.width, sizes.height);
    renderer.setPixelRatio(sizes.pixelRatio);

    // Controls
    const controls = new OrbitControls(camera, canvasRef.current);
    controls.enableDamping = true;

    // Resize
    const particles: ParticlesType = {};
    const onResize = () => {
      sizes.width = window.innerWidth;
      sizes.height = window.innerHeight;
      sizes.pixelRatio = Math.min(window.devicePixelRatio, 2);

      if (particles.material) {
        particles.material.uniforms.uResolution.value.set(
          sizes.width * sizes.pixelRatio,
          sizes.height * sizes.pixelRatio
        );
      }

      camera.aspect = sizes.width / sizes.height;
      camera.updateProjectionMatrix();
      renderer.setSize(sizes.width, sizes.height);
      renderer.setPixelRatio(sizes.pixelRatio);
    };
    window.addEventListener('resize', onResize);

    // Main async logic
    let animationId = 0;

    gltfLoader.load(MODEL_PATH, (gltf) => {
      // Base geometry
      const baseGeometry: { instance?: THREE.BufferGeometry; count?: number } =
        {};
      baseGeometry.instance = (gltf.scene.children[0] as THREE.Mesh).geometry;
      baseGeometry.count = baseGeometry.instance.attributes.position.count;

      // GPGPU
      const gpgpu: GPGPUType = {};
      gpgpu.size = Math.ceil(Math.sqrt(baseGeometry.count));
      gpgpu.computation = new GPUComputationRenderer(
        gpgpu.size,
        gpgpu.size,
        renderer
      );

      // Base particles texture
      const baseParticlesTexture = gpgpu.computation.createTexture();
      const data = baseParticlesTexture.image.data as Float32Array;
      for (let i = 0; i < baseGeometry.count!; i++) {
        const i3 = i * 3;
        const i4 = i * 4;
        data[i4 + 0] = baseGeometry.instance!.attributes.position.array[i3 + 0];
        data[i4 + 1] = baseGeometry.instance!.attributes.position.array[i3 + 1];
        data[i4 + 2] = baseGeometry.instance!.attributes.position.array[i3 + 2];
        data[i4 + 3] = Math.random();
      }

      // GPGPU variable
      gpgpu.particlesVariable = gpgpu.computation.addVariable(
        'uParticles',
        gpgpuParticlesShader,
        baseParticlesTexture
      );
      gpgpu.computation.setVariableDependencies(gpgpu.particlesVariable, [
        gpgpu.particlesVariable,
      ]);
      // Uniforms
      gpgpu.particlesVariable.material.uniforms.uTime = new THREE.Uniform(0);
      gpgpu.particlesVariable.material.uniforms.uDeltaTime = new THREE.Uniform(
        0
      );
      gpgpu.particlesVariable.material.uniforms.uBase = new THREE.Uniform(
        baseParticlesTexture
      );
      gpgpu.particlesVariable.material.uniforms.uFlowFieldInfluence =
        new THREE.Uniform(0.5);
      gpgpu.particlesVariable.material.uniforms.uFlowFieldStrength =
        new THREE.Uniform(2);
      gpgpu.particlesVariable.material.uniforms.uFlowFieldFrequency =
        new THREE.Uniform(0.5);
      gpgpu.computation.init();

      // Debug plane
      gpgpu.debug = new THREE.Mesh(
        new THREE.PlaneGeometry(3, 3),
        new THREE.MeshBasicMaterial({
          map: gpgpu.computation.getCurrentRenderTarget(gpgpu.particlesVariable)
            .texture,
        })
      );
      gpgpu.debug.position.x = 3;
      gpgpu.debug.visible = false;
      scene.add(gpgpu.debug);

      // Particles geometry
      const particlesUvArray = new Float32Array(baseGeometry.count! * 2);
      const sizesArray = new Float32Array(baseGeometry.count!);
      for (let y = 0; y < gpgpu.size!; y++) {
        for (let x = 0; x < gpgpu.size!; x++) {
          const i = y * gpgpu.size! + x;
          const i2 = i * 2;
          const uvX = (x + 0.5) / gpgpu.size!;
          const uvY = (y + 0.5) / gpgpu.size!;
          particlesUvArray[i2 + 0] = uvX;
          particlesUvArray[i2 + 1] = uvY;
          sizesArray[i] = Math.random();
        }
      }

      particles.geometry = new THREE.BufferGeometry();
      particles.geometry.setDrawRange(0, baseGeometry.count!);
      particles.geometry.setAttribute(
        'aParticlesUv',
        new THREE.BufferAttribute(particlesUvArray, 2)
      );
      if (baseGeometry.instance!.attributes.color) {
        particles.geometry.setAttribute(
          'aColor',
          baseGeometry.instance!.attributes.color
        );
      }
      particles.geometry.setAttribute(
        'aSize',
        new THREE.BufferAttribute(sizesArray, 1)
      );

      // Material
      particles.material = new THREE.ShaderMaterial({
        vertexShader: particlesVertexShader,
        fragmentShader: particlesFragmentShader,
        uniforms: {
          uSize: new THREE.Uniform(0.07),
          uResolution: new THREE.Uniform(
            new THREE.Vector2(
              sizes.width * sizes.pixelRatio,
              sizes.height * sizes.pixelRatio
            )
          ),
          uParticlesTexture: new THREE.Uniform(),
        },
      });

      // Points
      particles.points = new THREE.Points(
        particles.geometry,
        particles.material
      );
      scene.add(particles.points);

      // GUI
      gui.addColor(debugObject, 'clearColor').onChange(() => {
        renderer.setClearColor(debugObject.clearColor as string);
      });
      gui
        .add(particles.material.uniforms.uSize, 'value')
        .min(0)
        .max(1)
        .step(0.001)
        .name('uSize');
      gui
        .add(
          gpgpu.particlesVariable.material.uniforms.uFlowFieldInfluence,
          'value'
        )
        .min(0)
        .max(1)
        .step(0.001)
        .name('uFlowfieldInfluence');
      gui
        .add(
          gpgpu.particlesVariable.material.uniforms.uFlowFieldStrength,
          'value'
        )
        .min(0)
        .max(10)
        .step(0.001)
        .name('uFlowfieldStrength');
      gui
        .add(
          gpgpu.particlesVariable.material.uniforms.uFlowFieldFrequency,
          'value'
        )
        .min(0)
        .max(1)
        .step(0.001)
        .name('uFlowfieldFrequency');

      // Animate
      const clock = new THREE.Clock();
      let previousTime = 0;

      const tick = () => {
        const elapsedTime = clock.getElapsedTime();
        const deltaTime = elapsedTime - previousTime;
        previousTime = elapsedTime;

        controls.update();

        gpgpu.particlesVariable!.material.uniforms.uTime.value = elapsedTime;
        gpgpu.particlesVariable!.material.uniforms.uDeltaTime.value = deltaTime;
        gpgpu.computation!.compute();
        particles.material!.uniforms.uParticlesTexture.value =
          gpgpu.computation!.getCurrentRenderTarget(
            gpgpu.particlesVariable!
          ).texture;

        renderer.render(scene, camera);
        animationId = window.requestAnimationFrame(tick);
      };
      tick();
    });

    // 清理
    return () => {
      window.removeEventListener('resize', onResize);
      gui.destroy();
      renderer.dispose();
      scene.clear();
      if (animationId) window.cancelAnimationFrame(animationId);
    };
  }, []);

  return <canvas ref={canvasRef} className="webgl" />;
};

export default GPGPUFlowFieldParticles;

varying vec3 vColor;

void main()
{
    vec2 uv = gl_PointCoord;
    // float distanceToCenter = length(uv - vec2(0.5)); // same as below
    float distanceToCenter = distance(uv, vec2(0.5));

    if (distanceToCenter > 0.5) {
        discard; // simply drop after, prevent the fragment from being drawn entirely without even relying on transparency
        // may hace performance impact
    }

    gl_FragColor = vec4(vColor, 1.0);
    #include <tonemapping_fragment>
    #include <colorspace_fragment>
}

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import Pages from 'vite-plugin-pages'
import glsl from 'vite-plugin-glsl'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    Pages({
      dirs: 'src/course',
    }),
    glsl(),
    tailwindcss(),
  ],
  server: {
    watch: {
      ignored: ['!**/*.glsl'],
    },
  },
});

// it's the shader that will update the particles pixels by gpgpu technique
// it's here we apply to handle the flow field

// uniform sampler2D uParticles  // no need declare, it's already injected in the GPUComputationRenderer

void main()
{
    vec2 uv = gl_FragCoord.xy / resolution.xy; // need classic UV coordinates
    vec4 particle = texture(uParticles, uv); // coordinate

    // Apply the dynamic change with wrapping to keep values in reasonable range
    particle.y += 0.01;

    // Wrap Y value to prevent it from growing indefinitely
    if(particle.y > 3.0) {
        particle.y = -3.0;
    }

    // Output the modified particle data
    gl_FragColor = particle;

    // Debug: Visualize the data as colors (uncomment to see)
    // gl_FragColor = vec4((particle.xyz + 3.0) / 6.0, 1.0); // Map [-3,3] to [0,1]
}
